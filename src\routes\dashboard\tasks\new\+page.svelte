<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  let title = '';
  let subtitle = '';
  let notes = '';
  let priority = 0;
  let categoryId = '';
  let dueDate = '';
  let dueTime = '';
  let loading = false;
  let error = '';
  let success = '';

  const priorityOptions = [
    { value: 0, label: 'Low', color: '#3182ce' },
    { value: 1, label: 'Normal', color: '#319795' },
    { value: 2, label: 'High', color: '#c53030' }
  ];

  async function handleSubmit() {
    if (!title.trim()) {
      error = 'Title is required';
      return;
    }

    loading = true;
    error = '';

    try {
      // Combine date and time if both are provided
      let combinedDueDate = null;
      if (dueDate) {
        if (dueTime) {
          combinedDueDate = new Date(`${dueDate}T${dueTime}`);
        } else {
          combinedDueDate = new Date(`${dueDate}T09:00`); // Default to 9 AM
        }
      }

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          subtitle: subtitle.trim() || null,
          notes: notes.trim() || null,
          priority,
          categoryId: categoryId || null,
          dueDate: combinedDueDate?.toISOString() || null
        }),
      });

      const result = await response.json();

      if (response.ok) {
        success = 'Task created successfully!';
        setTimeout(() => goto('/dashboard'), 1500);
      } else {
        error = result.error || 'Failed to create task';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>New Task - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <h1 class="page-title">Create New Task</h1>
  <p class="page-subtitle">Add a new task to your routine</p>
</div>

<div class="form-container">
  <form on:submit|preventDefault={handleSubmit} class="task-form">
    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    <div class="form-group">
      <label for="title">Title *</label>
      <input
        id="title"
        type="text"
        bind:value={title}
        placeholder="Enter task title"
        required
        disabled={loading}
      />
    </div>

    <div class="form-group">
      <label for="subtitle">Subtitle</label>
      <input
        id="subtitle"
        type="text"
        bind:value={subtitle}
        placeholder="Enter task subtitle (optional)"
        disabled={loading}
      />
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="priority">Priority</label>
        <select id="priority" bind:value={priority} disabled={loading}>
          {#each priorityOptions as option}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
      </div>

      <div class="form-group">
        <label for="category">Category</label>
        <select id="category" bind:value={categoryId} disabled={loading}>
          <option value="">No category</option>
          {#each data.categories as category}
            <option value={category.id}>{category.name}</option>
          {/each}
        </select>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="dueDate">Due Date</label>
        <input
          id="dueDate"
          type="date"
          bind:value={dueDate}
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="dueTime">Due Time</label>
        <input
          id="dueTime"
          type="time"
          bind:value={dueTime}
          disabled={loading || !dueDate}
        />
      </div>
    </div>

    <div class="form-group">
      <label for="notes">Notes</label>
      <textarea
        id="notes"
        bind:value={notes}
        placeholder="Add any additional notes (optional)"
        rows="4"
        disabled={loading}
      ></textarea>
    </div>

    <div class="form-actions">
      <button type="button" class="btn-secondary" on:click={() => goto('/dashboard')} disabled={loading}>
        Cancel
      </button>
      <button type="submit" class="btn-primary" disabled={loading}>
        {loading ? 'Creating...' : 'Create Task'}
      </button>
    </div>
  </form>
</div>

<style>
  .page-header {
    margin-bottom: 2rem;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .form-container {
    max-width: 600px;
    margin: 0 auto;
  }

  .task-form {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  input:disabled, select:disabled, textarea:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 1rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .btn-secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }

  .btn-secondary:hover:not(:disabled) {
    background: rgba(237, 242, 247, 0.9);
  }

  .btn-primary:disabled, .btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .success-message {
    background: #f0fff4;
    color: #38a169;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
    }

    .form-actions {
      flex-direction: column;
    }

    .page-title {
      font-size: 1.875rem;
    }
  }
</style>
