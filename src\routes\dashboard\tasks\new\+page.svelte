<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  let title = '';
  let notes = '';
  let priority = 1;
  let categoryId = '';
  let dueDate = '';
  let dueTime = '';
  let subtasks: string[] = [''];
  let loading = false;
  let error = '';
  let success = '';

  // Recurrence rule settings
  let hasRecurrence = false;
  let recurrenceType = 'daily'; // daily, weekly, monthly, yearly, custom
  let recurrenceInterval = 1;
  let weeklyDays: number[] = []; // 0=Sunday, 1=Monday, etc.
  let monthlyType = 'date'; // 'date', 'weekday', 'last_day', 'last_weekday'
  let monthlyDate = 1;
  let monthlyWeekday = 1; // 1=Monday
  let monthlyWeekNumber = 1; // 1=first, 2=second, -1=last
  let endType = 'never'; // 'never', 'after', 'on'
  let endAfterOccurrences = 10;
  let endOnDate = '';

  const priorityOptions = [
    { value: 0, label: 'Low', color: '#64748b' },
    { value: 1, label: 'Normal', color: '#059669' },
    { value: 2, label: 'High', color: '#dc2626' }
  ];

  const weekDays = [
    { value: 1, label: 'Monday', short: 'Mon' },
    { value: 2, label: 'Tuesday', short: 'Tue' },
    { value: 3, label: 'Wednesday', short: 'Wed' },
    { value: 4, label: 'Thursday', short: 'Thu' },
    { value: 5, label: 'Friday', short: 'Fri' },
    { value: 6, label: 'Saturday', short: 'Sat' },
    { value: 0, label: 'Sunday', short: 'Sun' }
  ];

  const weekNumbers = [
    { value: 1, label: 'First' },
    { value: 2, label: 'Second' },
    { value: 3, label: 'Third' },
    { value: 4, label: 'Fourth' },
    { value: -1, label: 'Last' }
  ];

  function addSubtask() {
    subtasks = [...subtasks, ''];
  }

  function removeSubtask(index: number) {
    subtasks = subtasks.filter((_, i) => i !== index);
  }

  function toggleWeekDay(day: number) {
    if (weeklyDays.includes(day)) {
      weeklyDays = weeklyDays.filter(d => d !== day);
    } else {
      weeklyDays = [...weeklyDays, day].sort();
    }
  }

  function buildRecurrenceRule() {
    if (!hasRecurrence) return null;

    const rule: any = {
      type: recurrenceType,
      interval: recurrenceInterval
    };

    switch (recurrenceType) {
      case 'weekly':
        rule.weekdays = weeklyDays;
        break;
      case 'monthly':
        rule.monthlyType = monthlyType;
        if (monthlyType === 'date') {
          rule.monthlyDate = monthlyDate;
        } else if (monthlyType === 'weekday') {
          rule.monthlyWeekday = monthlyWeekday;
          rule.monthlyWeekNumber = monthlyWeekNumber;
        } else if (monthlyType === 'last_weekday') {
          rule.monthlyWeekday = monthlyWeekday;
        }
        break;
    }

    // End conditions
    rule.endType = endType;
    if (endType === 'after') {
      rule.endAfterOccurrences = endAfterOccurrences;
    } else if (endType === 'on') {
      rule.endOnDate = endOnDate;
    }

    return rule;
  }

  async function handleSubmit() {
    if (!title.trim()) {
      error = 'Title is required';
      return;
    }

    loading = true;
    error = '';

    try {
      // Combine date and time if both are provided
      let combinedDueDate = null;
      if (dueDate) {
        if (dueTime) {
          combinedDueDate = new Date(`${dueDate}T${dueTime}`);
        } else {
          combinedDueDate = new Date(`${dueDate}T09:00`); // Default to 9 AM
        }
      }

      // Filter out empty subtasks
      const validSubtasks = subtasks.filter(s => s.trim() !== '');

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          notes: notes.trim() || null,
          priority,
          categoryId: categoryId || null,
          dueDate: combinedDueDate?.toISOString() || null,
          subtasks: validSubtasks,
          recurrenceRule: buildRecurrenceRule()
        }),
      });

      const result = await response.json();

      if (response.ok) {
        success = 'Task created successfully!';
        setTimeout(() => goto('/dashboard'), 1500);
      } else {
        error = result.error || 'Failed to create task';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>New Task - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <h1 class="page-title">Create New Task</h1>
  <p class="page-subtitle">Add a new task to your routine</p>
</div>

<div class="form-container">
  <form on:submit|preventDefault={handleSubmit} class="task-form">
    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    <div class="form-group">
      <label for="title">Title *</label>
      <input
        id="title"
        type="text"
        bind:value={title}
        placeholder="Enter task title"
        required
        disabled={loading}
      />
    </div>

    <div class="form-group">
      <label>Subtasks</label>
      <div class="subtasks-container">
        {#each subtasks as subtask, index}
          <div class="subtask-row">
            <input
              type="text"
              bind:value={subtasks[index]}
              placeholder="Enter subtask"
              disabled={loading}
            />
            {#if subtasks.length > 1}
              <button
                type="button"
                class="remove-subtask-btn"
                on:click={() => removeSubtask(index)}
                disabled={loading}
              >
                ×
              </button>
            {/if}
          </div>
        {/each}
        <button
          type="button"
          class="add-subtask-btn"
          on:click={addSubtask}
          disabled={loading}
        >
          + Add Subtask
        </button>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="priority">Priority</label>
        <select id="priority" bind:value={priority} disabled={loading}>
          {#each priorityOptions as option}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
      </div>

      <div class="form-group">
        <label for="category">Category</label>
        <select id="category" bind:value={categoryId} disabled={loading}>
          <option value="">No category</option>
          {#each data.categories as category}
            <option value={category.id}>{category.name}</option>
          {/each}
        </select>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label for="dueDate">Due Date</label>
        <input
          id="dueDate"
          type="date"
          bind:value={dueDate}
          disabled={loading}
        />
      </div>

      <div class="form-group">
        <label for="dueTime">Due Time</label>
        <input
          id="dueTime"
          type="time"
          bind:value={dueTime}
          disabled={loading || !dueDate}
        />
      </div>
    </div>

    <div class="form-group">
      <label for="notes">Notes</label>
      <textarea
        id="notes"
        bind:value={notes}
        placeholder="Add any additional notes (optional)"
        rows="4"
        disabled={loading}
      ></textarea>
    </div>

    <!-- Recurrence Rules Section -->
    <div class="form-group">
      <div class="recurrence-header">
        <label>
          <input
            type="checkbox"
            bind:checked={hasRecurrence}
            disabled={loading}
          />
          Repeat Task
        </label>
      </div>

      {#if hasRecurrence}
        <div class="recurrence-config">
          <div class="form-row">
            <div class="form-group">
              <label for="recurrenceType">Repeat</label>
              <select id="recurrenceType" bind:value={recurrenceType} disabled={loading}>
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
                <option value="yearly">Yearly</option>
              </select>
            </div>

            <div class="form-group">
              <label for="interval">Every</label>
              <div class="interval-input">
                <input
                  id="interval"
                  type="number"
                  min="1"
                  max="99"
                  bind:value={recurrenceInterval}
                  disabled={loading}
                />
                <span class="interval-label">
                  {recurrenceType === 'daily' ? 'day(s)' :
                   recurrenceType === 'weekly' ? 'week(s)' :
                   recurrenceType === 'monthly' ? 'month(s)' : 'year(s)'}
                </span>
              </div>
            </div>
          </div>

          {#if recurrenceType === 'weekly'}
            <div class="form-group">
              <label>On days</label>
              <div class="weekdays-selector">
                {#each weekDays as day}
                  <button
                    type="button"
                    class="weekday-btn"
                    class:selected={weeklyDays.includes(day.value)}
                    on:click={() => toggleWeekDay(day.value)}
                    disabled={loading}
                  >
                    {day.short}
                  </button>
                {/each}
              </div>
            </div>
          {/if}

          {#if recurrenceType === 'monthly'}
            <div class="form-group">
              <label>Monthly pattern</label>
              <div class="monthly-options">
                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="date"
                    disabled={loading}
                  />
                  On day
                  <input
                    type="number"
                    min="1"
                    max="31"
                    bind:value={monthlyDate}
                    disabled={loading || monthlyType !== 'date'}
                    class="small-input"
                  />
                  of each month
                </label>

                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="weekday"
                    disabled={loading}
                  />
                  On the
                  <select bind:value={monthlyWeekNumber} disabled={loading || monthlyType !== 'weekday'}>
                    {#each weekNumbers as week}
                      <option value={week.value}>{week.label}</option>
                    {/each}
                  </select>
                  <select bind:value={monthlyWeekday} disabled={loading || monthlyType !== 'weekday'}>
                    {#each weekDays as day}
                      <option value={day.value}>{day.label}</option>
                    {/each}
                  </select>
                </label>

                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="last_day"
                    disabled={loading}
                  />
                  On the last day of each month
                </label>

                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="last_weekday"
                    disabled={loading}
                  />
                  On the last
                  <select bind:value={monthlyWeekday} disabled={loading || monthlyType !== 'last_weekday'}>
                    {#each weekDays as day}
                      <option value={day.value}>{day.label}</option>
                    {/each}
                  </select>
                  of each month
                </label>
              </div>
            </div>
          {/if}

          <div class="form-group">
            <label>End</label>
            <div class="end-options">
              <label class="radio-option">
                <input
                  type="radio"
                  bind:group={endType}
                  value="never"
                  disabled={loading}
                />
                Never
              </label>

              <label class="radio-option">
                <input
                  type="radio"
                  bind:group={endType}
                  value="after"
                  disabled={loading}
                />
                After
                <input
                  type="number"
                  min="1"
                  max="999"
                  bind:value={endAfterOccurrences}
                  disabled={loading || endType !== 'after'}
                  class="small-input"
                />
                occurrences
              </label>

              <label class="radio-option">
                <input
                  type="radio"
                  bind:group={endType}
                  value="on"
                  disabled={loading}
                />
                On
                <input
                  type="date"
                  bind:value={endOnDate}
                  disabled={loading || endType !== 'on'}
                />
              </label>
            </div>
          </div>
        </div>
      {/if}
    </div>

    <div class="form-actions">
      <button type="button" class="btn-secondary" on:click={() => goto('/dashboard')} disabled={loading}>
        Cancel
      </button>
      <button type="submit" class="btn-primary" disabled={loading}>
        {loading ? 'Creating...' : 'Create Task'}
      </button>
    </div>
  </form>
</div>

<style>
  .page-header {
    margin-bottom: 2rem;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .form-container {
    max-width: 600px;
    margin: 0 auto;
  }

  .task-form {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  input, select, textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  input:disabled, select:disabled, textarea:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 1rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .btn-secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }

  .btn-secondary:hover:not(:disabled) {
    background: rgba(237, 242, 247, 0.9);
  }

  .btn-primary:disabled, .btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .success-message {
    background: #f0fff4;
    color: #38a169;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  /* Subtasks styles */
  .subtasks-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .subtask-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .subtask-row input {
    flex: 1;
  }

  .remove-subtask-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #e5e7eb;
    background: #f9fafb;
    color: #6b7280;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    line-height: 1;
  }

  .remove-subtask-btn:hover:not(:disabled) {
    background: #fee2e2;
    color: #dc2626;
    border-color: #fecaca;
  }

  .add-subtask-btn {
    padding: 0.5rem 1rem;
    border: 1px dashed #d1d5db;
    background: transparent;
    color: #6b7280;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
  }

  .add-subtask-btn:hover:not(:disabled) {
    border-color: #4299e1;
    color: #4299e1;
    background: #f0f9ff;
  }

  /* Recurrence styles */
  .recurrence-header {
    margin-bottom: 1rem;
  }

  .recurrence-header label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    cursor: pointer;
  }

  .recurrence-header input[type="checkbox"] {
    width: auto;
    margin: 0;
  }

  .recurrence-config {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1rem;
  }

  .interval-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .interval-input input {
    width: 80px;
  }

  .interval-label {
    color: #6b7280;
    font-size: 0.875rem;
  }

  .weekdays-selector {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .weekday-btn {
    width: 40px;
    height: 40px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s;
  }

  .weekday-btn:hover:not(:disabled) {
    border-color: #4299e1;
    background: #f0f9ff;
  }

  .weekday-btn.selected {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
  }

  .monthly-options,
  .end-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .radio-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
  }

  .radio-option input[type="radio"] {
    width: auto;
    margin: 0;
  }

  .small-input {
    width: 60px !important;
    padding: 0.25rem 0.5rem !important;
    margin: 0 0.25rem !important;
  }

  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
    }

    .form-actions {
      flex-direction: column;
    }

    .page-title {
      font-size: 1.875rem;
    }
  }
</style>
