<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  let title = '';
  let notes = '';
  let priority = 1;
  let categoryId = '';
  let dueDate = '';
  let dueTime = '';
  let hasDueDate = false;
  let subtasks: string[] = [''];
  let loading = false;
  let error = '';
  let success = '';

  // Recurrence rule settings
  let hasRecurrence = false;
  let recurrenceType = 'daily'; // daily, weekly, monthly, yearly, custom
  let recurrenceInterval = 1;
  let weeklyDays: number[] = []; // 0=Sunday, 1=Monday, etc.
  let monthlyType = 'date'; // 'date', 'weekday', 'last_day', 'last_weekday'
  let monthlyDate = 1;
  let monthlyWeekday = 1; // 1=Monday
  let monthlyWeekNumber = 1; // 1=first, 2=second, -1=last
  let endType = 'never'; // 'never', 'after', 'on'
  let endAfterOccurrences = 10;
  let endOnDate = '';

  const priorityOptions = [
    { value: 0, label: 'Low', color: '#64748b' },
    { value: 1, label: 'Normal', color: '#059669' },
    { value: 2, label: 'High', color: '#dc2626' }
  ];

  const weekDays = [
    { value: 1, label: 'Monday', short: 'Mon' },
    { value: 2, label: 'Tuesday', short: 'Tue' },
    { value: 3, label: 'Wednesday', short: 'Wed' },
    { value: 4, label: 'Thursday', short: 'Thu' },
    { value: 5, label: 'Friday', short: 'Fri' },
    { value: 6, label: 'Saturday', short: 'Sat' },
    { value: 0, label: 'Sunday', short: 'Sun' }
  ];

  const weekNumbers = [
    { value: 1, label: 'First' },
    { value: 2, label: 'Second' },
    { value: 3, label: 'Third' },
    { value: 4, label: 'Fourth' },
    { value: -1, label: 'Last' }
  ];

  function addSubtask() {
    subtasks = [...subtasks, ''];
  }

  function removeSubtask(index: number) {
    subtasks = subtasks.filter((_, i) => i !== index);
  }

  function toggleWeekDay(day: number) {
    if (weeklyDays.includes(day)) {
      weeklyDays = weeklyDays.filter(d => d !== day);
    } else {
      weeklyDays = [...weeklyDays, day].sort();
    }
  }

  function buildRecurrenceRule() {
    if (!hasRecurrence) return null;

    const rule: any = {
      type: recurrenceType,
      interval: recurrenceInterval
    };

    switch (recurrenceType) {
      case 'weekly':
        rule.weekdays = weeklyDays;
        break;
      case 'monthly':
        rule.monthlyType = monthlyType;
        if (monthlyType === 'date') {
          rule.monthlyDate = monthlyDate;
        } else if (monthlyType === 'weekday') {
          rule.monthlyWeekday = monthlyWeekday;
          rule.monthlyWeekNumber = monthlyWeekNumber;
        } else if (monthlyType === 'last_weekday') {
          rule.monthlyWeekday = monthlyWeekday;
        }
        break;
    }

    // End conditions
    rule.endType = endType;
    if (endType === 'after') {
      rule.endAfterOccurrences = endAfterOccurrences;
    } else if (endType === 'on') {
      rule.endOnDate = endOnDate;
    }

    return rule;
  }

  async function handleSubmit() {
    if (!title.trim()) {
      error = 'Title is required';
      return;
    }

    loading = true;
    error = '';

    try {
      // Combine date and time if both are provided
      let combinedDueDate = null;
      if (hasDueDate && dueDate) {
        if (dueTime) {
          combinedDueDate = new Date(`${dueDate}T${dueTime}`);
        } else {
          combinedDueDate = new Date(`${dueDate}T09:00`); // Default to 9 AM
        }
      }

      // Filter out empty subtasks
      const validSubtasks = subtasks.filter(s => s.trim() !== '');

      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: title.trim(),
          notes: notes.trim() || null,
          priority,
          categoryId: categoryId || null,
          dueDate: combinedDueDate?.toISOString() || null,
          subtasks: validSubtasks,
          recurrenceRule: buildRecurrenceRule()
        }),
      });

      const result = await response.json();

      if (response.ok) {
        success = 'Task created successfully!';
        setTimeout(() => goto('/dashboard'), 1500);
      } else {
        error = result.error || 'Failed to create task';
      }
    } catch (err) {
      error = 'Network error. Please try again.';
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>New Task - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <h1 class="page-title">Create New Task</h1>
  <p class="page-subtitle">Add a new task to your routine</p>
</div>

<div class="form-container">
  <form on:submit|preventDefault={handleSubmit} class="task-form">
    {#if error}
      <div class="error-message">{error}</div>
    {/if}

    {#if success}
      <div class="success-message">{success}</div>
    {/if}

    <div class="form-group">
      <label for="title">Title *</label>
      <input
        id="title"
        type="text"
        bind:value={title}
        placeholder="Enter task title"
        required
        disabled={loading}
      />
    </div>

    <div class="form-group">
      <label>Subtasks</label>
      <div class="subtasks-container">
        {#each subtasks as subtask, index}
          <div class="subtask-row">
            <input
              type="text"
              bind:value={subtasks[index]}
              placeholder="Enter subtask"
              disabled={loading}
            />
            {#if subtasks.length > 1}
              <button
                type="button"
                class="remove-subtask-btn"
                on:click={() => removeSubtask(index)}
                disabled={loading}
              >
                ×
              </button>
            {/if}
          </div>
        {/each}
        <button
          type="button"
          class="add-subtask-btn"
          on:click={addSubtask}
          disabled={loading}
        >
          + Add Subtask
        </button>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label>Priority</label>
        <div class="priority-selector">
          {#each priorityOptions as option}
            <button
              type="button"
              class="priority-btn"
              class:selected={priority === option.value}
              style="--priority-color: {option.color}"
              on:click={() => priority = option.value}
              disabled={loading}
            >
              <div class="priority-indicator"></div>
              {option.label}
            </button>
          {/each}
        </div>
      </div>

      <div class="form-group">
        <label>Category</label>
        <div class="category-selector">
          <button
            type="button"
            class="category-btn"
            class:selected={!categoryId}
            on:click={() => categoryId = ''}
            disabled={loading}
          >
            <div class="category-color" style="background: #e5e7eb;"></div>
            No category
          </button>
          {#each data.categories as category}
            <button
              type="button"
              class="category-btn"
              class:selected={categoryId === category.id}
              on:click={() => categoryId = category.id}
              disabled={loading}
            >
              <div class="category-color" style="background: {category.color};"></div>
              {category.name}
            </button>
          {/each}
        </div>
      </div>
    </div>

    <div class="form-group">
      <div class="due-date-header">
        <label>
          <input
            type="checkbox"
            bind:checked={hasDueDate}
            disabled={loading}
          />
          Set Due Date
        </label>
      </div>

      {#if hasDueDate}
        <div class="due-date-container">
          <div class="date-input-group">
            <label for="dueDate">Date</label>
            <input
              id="dueDate"
              type="date"
              bind:value={dueDate}
              disabled={loading}
              class="modern-input"
            />
          </div>

          <div class="time-input-group">
            <label for="dueTime">Time (optional)</label>
            <input
              id="dueTime"
              type="time"
              bind:value={dueTime}
              disabled={loading}
              class="modern-input"
              placeholder="09:00"
            />
          </div>
        </div>
      {/if}
    </div>

    <div class="form-group">
      <label for="notes">Notes</label>
      <textarea
        id="notes"
        bind:value={notes}
        placeholder="Add any additional notes (optional)"
        rows="4"
        disabled={loading}
      ></textarea>
    </div>

    <!-- Recurrence Rules Section -->
    <div class="form-group">
      <div class="recurrence-header">
        <label>
          <input
            type="checkbox"
            bind:checked={hasRecurrence}
            disabled={loading}
          />
          Repeat Task
        </label>
      </div>

      {#if hasRecurrence}
        <div class="recurrence-config">
          <div class="recurrence-type-selector">
            <label>Repeat</label>
            <div class="type-buttons">
              <button
                type="button"
                class="type-btn"
                class:selected={recurrenceType === 'daily'}
                on:click={() => recurrenceType = 'daily'}
                disabled={loading}
              >
                Daily
              </button>
              <button
                type="button"
                class="type-btn"
                class:selected={recurrenceType === 'weekly'}
                on:click={() => recurrenceType = 'weekly'}
                disabled={loading}
              >
                Weekly
              </button>
              <button
                type="button"
                class="type-btn"
                class:selected={recurrenceType === 'monthly'}
                on:click={() => recurrenceType = 'monthly'}
                disabled={loading}
              >
                Monthly
              </button>
              <button
                type="button"
                class="type-btn"
                class:selected={recurrenceType === 'yearly'}
                on:click={() => recurrenceType = 'yearly'}
                disabled={loading}
              >
                Yearly
              </button>
            </div>
          </div>

          <div class="interval-selector">
            <label>Every</label>
            <div class="interval-input">
              <input
                type="number"
                min="1"
                max="99"
                bind:value={recurrenceInterval}
                disabled={loading}
                class="interval-number"
              />
              <span class="interval-label">
                {recurrenceType === 'daily' ? 'day(s)' :
                 recurrenceType === 'weekly' ? 'week(s)' :
                 recurrenceType === 'monthly' ? 'month(s)' : 'year(s)'}
              </span>
            </div>
          </div>

          {#if recurrenceType === 'weekly'}
            <div class="form-group">
              <label>On days</label>
              <div class="weekdays-selector">
                {#each weekDays as day}
                  <button
                    type="button"
                    class="weekday-btn"
                    class:selected={weeklyDays.includes(day.value)}
                    on:click={() => toggleWeekDay(day.value)}
                    disabled={loading}
                  >
                    {day.short}
                  </button>
                {/each}
              </div>
            </div>
          {/if}

          {#if recurrenceType === 'monthly'}
            <div class="form-group">
              <label>Monthly pattern</label>
              <div class="monthly-options">
                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="date"
                    disabled={loading}
                  />
                  On day
                  <input
                    type="number"
                    min="1"
                    max="31"
                    bind:value={monthlyDate}
                    disabled={loading || monthlyType !== 'date'}
                    class="small-input"
                  />
                  of each month
                </label>

                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="weekday"
                    disabled={loading}
                  />
                  On the
                  <select bind:value={monthlyWeekNumber} disabled={loading || monthlyType !== 'weekday'}>
                    {#each weekNumbers as week}
                      <option value={week.value}>{week.label}</option>
                    {/each}
                  </select>
                  <select bind:value={monthlyWeekday} disabled={loading || monthlyType !== 'weekday'}>
                    {#each weekDays as day}
                      <option value={day.value}>{day.label}</option>
                    {/each}
                  </select>
                </label>

                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="last_day"
                    disabled={loading}
                  />
                  On the last day of each month
                </label>

                <label class="radio-option">
                  <input
                    type="radio"
                    bind:group={monthlyType}
                    value="last_weekday"
                    disabled={loading}
                  />
                  On the last
                  <select bind:value={monthlyWeekday} disabled={loading || monthlyType !== 'last_weekday'}>
                    {#each weekDays as day}
                      <option value={day.value}>{day.label}</option>
                    {/each}
                  </select>
                  of each month
                </label>
              </div>
            </div>
          {/if}

          <div class="form-group">
            <label>End</label>
            <div class="end-options">
              <label class="radio-option">
                <input
                  type="radio"
                  bind:group={endType}
                  value="never"
                  disabled={loading}
                />
                Never
              </label>

              <label class="radio-option">
                <input
                  type="radio"
                  bind:group={endType}
                  value="after"
                  disabled={loading}
                />
                After
                <input
                  type="number"
                  min="1"
                  max="999"
                  bind:value={endAfterOccurrences}
                  disabled={loading || endType !== 'after'}
                  class="small-input"
                />
                occurrences
              </label>

              <label class="radio-option">
                <input
                  type="radio"
                  bind:group={endType}
                  value="on"
                  disabled={loading}
                />
                On
                <input
                  type="date"
                  bind:value={endOnDate}
                  disabled={loading || endType !== 'on'}
                />
              </label>
            </div>
          </div>
        </div>
      {/if}
    </div>

    <div class="form-actions">
      <button type="button" class="btn-secondary" on:click={() => goto('/dashboard')} disabled={loading}>
        Cancel
      </button>
      <button type="submit" class="btn-primary" disabled={loading}>
        {loading ? 'Creating...' : 'Create Task'}
      </button>
    </div>
  </form>
</div>

<style>
  .page-header {
    margin-bottom: 2rem;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .form-container {
    max-width: 700px;
    margin: 0 auto;
  }

  .task-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.3);
    padding: 2.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  input, textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease;
  }

  input:focus, textarea:focus {
    outline: none;
    border-color: #4299e1;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 4px rgba(66, 153, 225, 0.1);
    transform: translateY(-1px);
  }

  input:disabled, textarea:disabled {
    background-color: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
  }

  .modern-input {
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 0.875rem;
  }

  .modern-input:focus {
    background: white;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.08);
  }

  textarea {
    resize: vertical;
    min-height: 100px;
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
    font-size: 1rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .btn-secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }

  .btn-secondary:hover:not(:disabled) {
    background: rgba(237, 242, 247, 0.9);
  }

  .btn-primary:disabled, .btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  .success-message {
    background: #f0fff4;
    color: #38a169;
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-weight: 500;
  }

  /* Priority selector styles */
  .priority-selector {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .priority-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    color: #374151;
  }

  .priority-btn:hover:not(:disabled) {
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .priority-btn.selected {
    border-color: var(--priority-color);
    background: rgba(66, 153, 225, 0.05);
    color: var(--priority-color);
  }

  .priority-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--priority-color);
  }

  /* Category selector styles */
  .category-selector {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-height: 200px;
    overflow-y: auto;
  }

  .category-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
    font-weight: 500;
    color: #374151;
  }

  .category-btn:hover:not(:disabled) {
    border-color: #d1d5db;
    background: #f9fafb;
  }

  .category-btn.selected {
    border-color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
    color: #2563eb;
  }

  .category-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  /* Due date styles */
  .due-date-header {
    margin-bottom: 1rem;
  }

  .due-date-header label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    cursor: pointer;
  }

  .due-date-header input[type="checkbox"] {
    width: auto;
    margin: 0;
  }

  .due-date-container {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  .date-input-group,
  .time-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .date-input-group label,
  .time-input-group label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
  }

  /* Subtasks styles */
  .subtasks-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .subtask-row {
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }

  .subtask-row input {
    flex: 1;
    padding: 0.75rem;
    border-radius: 10px;
  }

  .remove-subtask-btn {
    width: 36px;
    height: 36px;
    border: 1px solid #e5e7eb;
    background: white;
    color: #6b7280;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    line-height: 1;
    transition: all 0.2s ease;
  }

  .remove-subtask-btn:hover:not(:disabled) {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;
    transform: scale(1.05);
  }

  .add-subtask-btn {
    padding: 0.75rem 1.25rem;
    border: 2px dashed #d1d5db;
    background: transparent;
    color: #6b7280;
    border-radius: 12px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  .add-subtask-btn:hover:not(:disabled) {
    border-color: #4299e1;
    color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
    transform: translateY(-1px);
  }

  /* Recurrence styles */
  .recurrence-header {
    margin-bottom: 1rem;
  }

  .recurrence-header label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    cursor: pointer;
  }

  .recurrence-header input[type="checkbox"] {
    width: auto;
    margin: 0;
  }

  .recurrence-config {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 2rem;
    margin-top: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  /* Recurrence type selector */
  .recurrence-type-selector {
    margin-bottom: 1.5rem;
  }

  .type-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    margin-top: 0.75rem;
  }

  .type-btn {
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    background: white;
    color: #374151;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;
  }

  .type-btn:hover:not(:disabled) {
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .type-btn.selected {
    border-color: #4299e1;
    background: #4299e1;
    color: white;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  }

  /* Interval selector */
  .interval-selector {
    margin-bottom: 1.5rem;
  }

  .interval-input {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.75rem;
  }

  .interval-number {
    width: 80px !important;
    padding: 0.75rem !important;
    text-align: center;
    font-weight: 600;
  }

  .interval-label {
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .weekdays-selector {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .weekday-btn {
    width: 44px;
    height: 44px;
    border: 2px solid #e5e7eb;
    background: white;
    color: #374151;
    border-radius: 12px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .weekday-btn:hover:not(:disabled) {
    border-color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.15);
  }

  .weekday-btn.selected {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
  }

  .monthly-options,
  .end-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .radio-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: normal;
    cursor: pointer;
  }

  .radio-option input[type="radio"] {
    width: auto;
    margin: 0;
  }

  .small-input {
    width: 70px !important;
    padding: 0.5rem !important;
    margin: 0 0.5rem !important;
    text-align: center;
    font-weight: 600;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .task-form {
      padding: 1.5rem;
      margin: 1rem;
      border-radius: 16px;
    }

    .form-row {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .due-date-container {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .priority-selector {
      grid-template-columns: repeat(3, 1fr);
    }

    .type-buttons {
      grid-template-columns: repeat(2, 1fr);
    }

    .weekdays-selector {
      justify-content: center;
    }

    .category-selector {
      max-height: 150px;
    }

    .monthly-options,
    .end-options {
      gap: 1rem;
    }

    .radio-option {
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .interval-input {
      flex-wrap: wrap;
      justify-content: center;
    }

    .form-actions {
      flex-direction: column;
      gap: 1rem;
    }

    .form-actions button {
      width: 100%;
    }
  }

  @media (max-width: 480px) {
    .form-container {
      padding: 0.5rem;
    }

    .task-form {
      padding: 1rem;
    }

    .priority-selector {
      grid-template-columns: 1fr;
    }

    .type-buttons {
      grid-template-columns: 1fr;
    }

    .weekdays-selector {
      grid-template-columns: repeat(4, 1fr);
      gap: 0.5rem;
    }

    .weekday-btn {
      width: 100%;
      height: 40px;
    }
  }

  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
    }

    .form-actions {
      flex-direction: column;
    }

    .page-title {
      font-size: 1.875rem;
    }
  }
</style>
