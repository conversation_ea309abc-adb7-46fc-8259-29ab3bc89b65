<script lang="ts">
  import type { PageData } from './$types';

  export let data: PageData;

  // Mock data for dashboard
  const metrics = [
    { value: '12', label: 'Today', trend: '+2', trendType: 'up' },
    { value: '3', label: 'Overdue', trend: '-1', trendType: 'down' },
    { value: '8', label: 'This Week', trend: '+5', trendType: 'up' },
    { value: '95%', label: 'Completed', trend: '+3%', trendType: 'up' }
  ];

  const tasks = [
    {
      id: 1,
      name: 'Review quarterly budget report',
      info: 'Due today at 5:00 PM • Finance',
      priority: 'urgent',
      completed: false
    },
    {
      id: 2,
      name: 'Team standup meeting',
      info: 'Due today at 10:00 AM • Recurring',
      priority: 'normal',
      completed: false
    },
    {
      id: 3,
      name: 'Send project update to stakeholders',
      info: 'Completed at 9:30 AM',
      priority: 'low',
      completed: true
    }
  ];

  const upcomingTasks = [
    { date: 'Tomorrow', task: 'Monthly report submission' },
    { date: 'Friday', task: 'Client presentation' },
    { date: 'Next Monday', task: 'Project kickoff meeting' }
  ];

  function getCurrentDate() {
    const options: Intl.DateTimeFormatOptions = { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    return new Date().toLocaleDateString('en-US', options);
  }
</script>

<div class="page-header">
  <h1 class="page-title">Dashboard</h1>
  <p class="page-subtitle">{getCurrentDate()}</p>
</div>

<div class="metrics-row">
  {#each metrics as metric}
    <div class="metric-card">
      <div class="metric-trend trend-{metric.trendType}">{metric.trend}</div>
      <div class="metric-value">{metric.value}</div>
      <div class="metric-label">{metric.label}</div>
    </div>
  {/each}
</div>

<div class="content-grid">
  <div class="main-panel">
    <div class="panel-header">
      <h2 class="panel-title">
        <span class="status-dot"></span>
        Active Tasks
      </h2>
    </div>
    <div class="task-list">
      {#each tasks as task}
        <div class="task-row">
          <input
            type="checkbox"
            class="task-check"
            checked={task.completed}
          />
          <div class="task-details">
            <div class="task-name">{task.name}</div>
            <div class="task-info">{task.info}</div>
          </div>
          <div class="task-badge badge-{task.priority}">{task.priority}</div>
        </div>
      {/each}
    </div>
  </div>

  <div class="side-panel">
    <div class="widget">
      <h3 class="widget-title">Actions</h3>
      <button class="action-btn">+ New Task</button>
      <a href="/dashboard/settings" class="action-btn secondary">Account Settings</a>
      <button class="action-btn secondary">View Calendar</button>
    </div>

    <div class="widget">
      <h3 class="widget-title">Upcoming</h3>
      <ul class="upcoming-list">
        {#each upcomingTasks as item}
          <li class="upcoming-item">
            <div class="upcoming-date">{item.date}</div>
            <div class="upcoming-task">{item.task}</div>
          </li>
        {/each}
      </ul>
    </div>
  </div>
</div>

<style>
  .page-header {
    margin-bottom: 3rem;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .metric-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .metric-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4299e1, #63b3ed);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .metric-card:hover {
    border-color: rgba(66, 153, 225, 0.3);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
    transform: translateY(-2px);
  }

  .metric-card:hover::before {
    opacity: 1;
  }

  .metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
  }

  .metric-label {
    color: #718096;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .metric-trend {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 600;
  }

  .trend-up {
    background: #f0fff4;
    color: #38a169;
  }

  .trend-down {
    background: #fed7d7;
    color: #e53e3e;
  }

  .content-grid {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 2rem;
  }

  .main-panel {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .panel-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8f9fa;
  }

  .panel-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4299e1;
  }

  .task-list {
    padding: 0;
  }

  .task-row {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #f7fafc;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: background-color 0.15s;
  }

  .task-row:hover {
    background: rgba(66, 153, 225, 0.05);
  }

  .task-row:last-child {
    border-bottom: none;
  }

  .task-check {
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .task-check:hover {
    border-color: #4299e1;
  }

  .task-details {
    flex: 1;
  }

  .task-name {
    font-weight: 500;
    color: #1a202c;
    margin-bottom: 0.25rem;
  }

  .task-info {
    font-size: 0.875rem;
    color: #718096;
  }

  .task-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .badge-urgent {
    background: #fed7d7;
    color: #c53030;
  }

  .badge-normal {
    background: #e6fffa;
    color: #319795;
  }

  .badge-low {
    background: #ebf8ff;
    color: #3182ce;
  }

  .side-panel {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .widget {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .widget-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 1rem;
  }

  .action-btn {
    display: block;
    width: 100%;
    padding: 0.875rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
    text-decoration: none;
    text-align: center;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .action-btn:hover {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .action-btn.secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .action-btn.secondary:hover {
    background: rgba(237, 242, 247, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .upcoming-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .upcoming-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f7fafc;
  }

  .upcoming-item:last-child {
    border-bottom: none;
  }

  .upcoming-date {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
  }

  .upcoming-task {
    font-weight: 500;
    color: #1a202c;
  }

  @media (max-width: 768px) {
    .content-grid {
      grid-template-columns: 1fr;
    }

    .metrics-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .page-title {
      font-size: 1.875rem;
    }
  }
</style>
