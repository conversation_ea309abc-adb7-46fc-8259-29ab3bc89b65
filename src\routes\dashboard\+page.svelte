<script lang="ts">
  import type { PageData } from './$types';
  import { goto } from '$app/navigation';

  export let data: PageData;

  // Calculate metrics from real data
  const metrics = [
    {
      value: data.metrics.todayCount.toString(),
      label: 'Today',
      trend: data.metrics.todayCount > 0 ? `+${data.metrics.todayCount}` : '0',
      trendType: data.metrics.todayCount > 0 ? 'up' : 'neutral'
    },
    {
      value: data.metrics.overdueCount.toString(),
      label: 'Overdue',
      trend: data.metrics.overdueCount > 0 ? `${data.metrics.overdueCount}` : '0',
      trendType: data.metrics.overdueCount > 0 ? 'down' : 'neutral'
    },
    {
      value: data.metrics.thisWeekCount.toString(),
      label: 'This Week',
      trend: data.metrics.thisWeekCount > 0 ? `+${data.metrics.thisWeekCount}` : '0',
      trendType: data.metrics.thisWeekCount > 0 ? 'up' : 'neutral'
    },
    {
      value: `${data.metrics.completionRate}%`,
      label: 'Completed',
      trend: `${data.metrics.completionRate}%`,
      trendType: data.metrics.completionRate >= 80 ? 'up' : data.metrics.completionRate >= 50 ? 'neutral' : 'down'
    }
  ];

  // Get recent tasks for display (mix of today, overdue, and upcoming)
  const displayTasks = [
    ...data.tasks.overdue.slice(0, 2),
    ...data.tasks.today.slice(0, 3),
    ...data.tasks.active.filter(task =>
      !data.tasks.today.includes(task) &&
      !data.tasks.overdue.includes(task)
    ).slice(0, 2)
  ].slice(0, 5);

  // Get upcoming tasks for sidebar
  const upcomingTasks = data.tasks.active
    .filter(task => task.dueDate && new Date(task.dueDate) > new Date())
    .slice(0, 3)
    .map(task => ({
      date: formatRelativeDate(new Date(task.dueDate!)),
      task: task.title
    }));

  function getCurrentDate() {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return new Date().toLocaleDateString('en-US', options);
  }

  function formatRelativeDate(date: Date): string {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      const diffTime = date.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays <= 7) {
        return date.toLocaleDateString('en-US', { weekday: 'long' });
      } else {
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }
    }
  }

  function formatTaskInfo(task: any): string {
    const category = data.categories.find(c => c.id === task.categoryId);
    const categoryName = category ? category.name : '';

    if (task.completed) {
      return `Completed ${task.completedAt ? new Date(task.completedAt).toLocaleDateString() : ''}`;
    }

    if (task.dueDate) {
      const dueDate = new Date(task.dueDate);
      const timeStr = dueDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' });
      const dateStr = formatRelativeDate(dueDate);
      return `Due ${dateStr} at ${timeStr}${categoryName ? ' • ' + categoryName : ''}`;
    }

    return categoryName || 'No due date';
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 2: return 'high';
      case 1: return 'normal';
      default: return 'low';
    }
  }

  async function completeTask(taskId: string) {
    try {
      const response = await fetch(`/api/tasks/${taskId}/complete`, {
        method: 'POST'
      });

      if (response.ok) {
        // Refresh the page to update data
        location.reload();
      } else {
        console.error('Failed to complete task');
      }
    } catch (error) {
      console.error('Error completing task:', error);
    }
  }
</script>

<div class="page-header">
  <h1 class="page-title">Dashboard</h1>
  <p class="page-subtitle">{getCurrentDate()}</p>
</div>

<div class="metrics-row">
  {#each metrics as metric}
    <div class="metric-card">
      <div class="metric-trend trend-{metric.trendType}">{metric.trend}</div>
      <div class="metric-value">{metric.value}</div>
      <div class="metric-label">{metric.label}</div>
    </div>
  {/each}
</div>

<div class="content-grid">
  <div class="main-panel">
    <div class="panel-header">
      <h2 class="panel-title">
        <span class="status-dot"></span>
        Active Tasks
      </h2>
    </div>
    <div class="task-list">
      {#each displayTasks as task}
        <div class="task-row">
          <input
            type="checkbox"
            class="task-check"
            checked={task.completed}
            on:change={() => !task.completed && completeTask(task.id)}
            disabled={task.completed}
          />
          <div class="task-details">
            <div class="task-name" class:completed={task.completed}>{task.title}</div>
            <div class="task-info">{formatTaskInfo(task)}</div>
          </div>
          <div class="task-badge badge-{getPriorityLabel(task.priority)}">{getPriorityLabel(task.priority)}</div>
        </div>
      {/each}

      {#if displayTasks.length === 0}
        <div class="empty-state">
          <p>No active tasks. Great job! 🎉</p>
          <button class="action-btn" on:click={() => goto('/dashboard/tasks/new')}>Create your first task</button>
        </div>
      {/if}
    </div>
  </div>

  <div class="side-panel">
    <div class="widget">
      <h3 class="widget-title">Actions</h3>
      <a href="/dashboard/tasks/new" class="action-btn">+ New Task</a>
      <a href="/dashboard/tasks" class="action-btn secondary">View All Tasks</a>
      <a href="/dashboard/settings" class="action-btn secondary">Account Settings</a>
    </div>

    <div class="widget">
      <h3 class="widget-title">Upcoming</h3>
      <ul class="upcoming-list">
        {#each upcomingTasks as item}
          <li class="upcoming-item">
            <div class="upcoming-date">{item.date}</div>
            <div class="upcoming-task">{item.task}</div>
          </li>
        {/each}
      </ul>
    </div>
  </div>
</div>

<style>
  .page-header {
    margin-bottom: 3rem;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .metrics-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
  }

  .metric-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .metric-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4299e1, #63b3ed);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .metric-card:hover {
    border-color: rgba(66, 153, 225, 0.3);
    box-shadow: 0 8px 25px rgba(66, 153, 225, 0.15);
    transform: translateY(-2px);
  }

  .metric-card:hover::before {
    opacity: 1;
  }

  .metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
  }

  .metric-label {
    color: #718096;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .metric-trend {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 600;
  }

  .trend-up {
    background: #f0fff4;
    color: #38a169;
  }

  .trend-down {
    background: #fed7d7;
    color: #e53e3e;
  }

  .trend-neutral {
    background: #f7fafc;
    color: #718096;
  }

  .content-grid {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 2rem;
  }

  .main-panel {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .panel-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8f9fa;
  }

  .panel-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4299e1;
  }

  .task-list {
    padding: 0;
  }

  .task-row {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #f7fafc;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: background-color 0.15s;
  }

  .task-row:hover {
    background: rgba(66, 153, 225, 0.05);
  }

  .task-row:last-child {
    border-bottom: none;
  }

  .task-check {
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .task-check:hover {
    border-color: #4299e1;
  }

  .task-check:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .task-details {
    flex: 1;
  }

  .task-name {
    font-weight: 500;
    color: #1a202c;
    margin-bottom: 0.25rem;
  }

  .task-name.completed {
    text-decoration: line-through;
    color: #718096;
  }

  .task-info {
    font-size: 0.875rem;
    color: #718096;
  }

  .task-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .badge-urgent {
    background: #fed7d7;
    color: #c53030;
  }

  .badge-normal {
    background: #e6fffa;
    color: #319795;
  }

  .badge-low {
    background: #ebf8ff;
    color: #3182ce;
  }

  .badge-high {
    background: #fed7d7;
    color: #c53030;
  }

  .empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: #718096;
  }

  .empty-state p {
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
  }

  .side-panel {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .widget {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .widget-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 1rem;
  }

  .action-btn {
    display: block;
    width: 100%;
    padding: 0.875rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
    text-decoration: none;
    text-align: center;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
    box-sizing: border-box;
    margin-bottom: 0.75rem;
  }

  .action-btn:hover {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .action-btn.secondary {
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .action-btn:last-child {
    margin-bottom: 0;
  }

  .action-btn.secondary:hover {
    background: rgba(237, 242, 247, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .upcoming-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .upcoming-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f7fafc;
  }

  .upcoming-item:last-child {
    border-bottom: none;
  }

  .upcoming-date {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
  }

  .upcoming-task {
    font-weight: 500;
    color: #1a202c;
  }

  @media (max-width: 768px) {
    .content-grid {
      grid-template-columns: 1fr;
    }

    .metrics-row {
      grid-template-columns: repeat(2, 1fr);
    }

    .page-title {
      font-size: 1.875rem;
    }
  }
</style>
