<script lang="ts">
  import { goto } from '$app/navigation';
  import type { LayoutData } from './$types';

  export let data: LayoutData;

  async function handleLogout() {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      goto('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
</script>

<svelte:head>
  <title>Dashboard - Routine Mail</title>
</svelte:head>

<div class="dashboard-layout">
  <header class="header">
    <div class="header-content">
      <div class="logo">Routine Mail</div>
      <div class="user-section">
        <div class="notification-badge">🔔</div>
        <span class="user-email">{data.user.email}</span>
        <a href="/dashboard/settings" class="settings-btn">Settings</a>
        <button class="logout-btn" on:click={handleLogout}>Logout</button>
      </div>
    </div>
  </header>

  <main class="main-content">
    <slot />
  </main>
</div>

<style>
  :global(body) {
    font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    color: #1a202c;
    line-height: 1.6;
    margin: 0;
    padding: 0;
  }

  .dashboard-layout {
    min-height: 100vh;
  }

  .header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    padding: 1.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: #2d3748;
    letter-spacing: -0.025em;
  }

  .logo::after {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #4299e1, #63b3ed);
    border-radius: 50%;
    margin-left: 0.5rem;
    vertical-align: middle;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .user-section {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .notification-badge {
    position: relative;
    padding: 0.5rem;
    border-radius: 8px;
    background: #f7fafc;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .notification-badge:hover {
    background: #edf2f7;
  }

  .notification-badge::after {
    content: "3";
    position: absolute;
    top: -4px;
    right: -4px;
    background: #e53e3e;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .user-email {
    color: #718096;
    font-weight: 500;
  }

  .settings-btn,
  .logout-btn {
    padding: 0.5rem 1rem;
    background: rgba(247, 250, 252, 0.8);
    color: #4a5568;
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 36px;
    min-width: 80px;
    box-sizing: border-box;
    margin: 0;
    outline: none;
    line-height: 1;
    white-space: nowrap;
  }

  .settings-btn:hover,
  .logout-btn:hover {
    background: rgba(237, 242, 247, 0.9);
    transform: translateY(-1px);
  }

  .main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 3rem 2rem;
  }

  @media (max-width: 768px) {
    .header-content {
      padding: 0 1rem;
    }

    .main-content {
      padding: 2rem 1rem;
    }

    .user-section {
      gap: 0.5rem;
    }

    .user-email {
      display: none;
    }
  }
</style>
