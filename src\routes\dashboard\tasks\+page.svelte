<script lang="ts">
  import { goto } from '$app/navigation';
  import type { PageData } from './$types';

  export let data: PageData;

  let filter = 'all'; // 'all', 'active', 'completed', 'today', 'overdue'
  let searchQuery = '';

  $: filteredTasks = data.tasks.all.filter(task => {
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!task.title.toLowerCase().includes(query) && 
          !task.subtitle?.toLowerCase().includes(query) &&
          !task.notes?.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Apply status filter
    switch (filter) {
      case 'active':
        return !task.completed;
      case 'completed':
        return task.completed;
      case 'today':
        return data.tasks.today.includes(task);
      case 'overdue':
        return data.tasks.overdue.includes(task);
      default:
        return true;
    }
  });

  function formatDate(dateString: string | null): string {
    if (!dateString) return 'No due date';
    
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return `Today at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return `Tomorrow at ${date.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit'
      });
    }
  }

  function getPriorityLabel(priority: number): string {
    switch (priority) {
      case 2: return 'High';
      case 1: return 'Normal';
      default: return 'Low';
    }
  }

  function getCategoryName(categoryId: string | null): string {
    if (!categoryId) return '';
    const category = data.categories.find(c => c.id === categoryId);
    return category?.name || '';
  }

  async function toggleTask(taskId: string, completed: boolean) {
    try {
      if (!completed) {
        // Complete the task
        const response = await fetch(`/api/tasks/${taskId}/complete`, {
          method: 'POST'
        });
        
        if (response.ok) {
          location.reload();
        }
      } else {
        // Uncomplete the task
        const response = await fetch(`/api/tasks/${taskId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ completed: false, completedAt: null })
        });
        
        if (response.ok) {
          location.reload();
        }
      }
    } catch (error) {
      console.error('Error toggling task:', error);
    }
  }

  async function deleteTask(taskId: string) {
    if (!confirm('Are you sure you want to delete this task?')) {
      return;
    }

    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        location.reload();
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  }
</script>

<svelte:head>
  <title>Tasks - Routine Mail</title>
</svelte:head>

<div class="page-header">
  <div class="header-content">
    <div>
      <h1 class="page-title">Tasks</h1>
      <p class="page-subtitle">Manage your tasks and routines</p>
    </div>
    <a href="/dashboard/tasks/new" class="btn-primary">+ New Task</a>
  </div>
</div>

<div class="filters-section">
  <div class="search-box">
    <input
      type="text"
      placeholder="Search tasks..."
      bind:value={searchQuery}
    />
  </div>
  
  <div class="filter-tabs">
    <button 
      class="filter-tab" 
      class:active={filter === 'all'}
      on:click={() => filter = 'all'}
    >
      All ({data.tasks.all.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'active'}
      on:click={() => filter = 'active'}
    >
      Active ({data.tasks.active.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'today'}
      on:click={() => filter = 'today'}
    >
      Today ({data.tasks.today.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'overdue'}
      on:click={() => filter = 'overdue'}
    >
      Overdue ({data.tasks.overdue.length})
    </button>
    <button 
      class="filter-tab" 
      class:active={filter === 'completed'}
      on:click={() => filter = 'completed'}
    >
      Completed ({data.tasks.completed.length})
    </button>
  </div>
</div>

<div class="tasks-container">
  {#if filteredTasks.length === 0}
    <div class="empty-state">
      {#if searchQuery}
        <p>No tasks found matching "{searchQuery}"</p>
      {:else if filter === 'completed'}
        <p>No completed tasks yet</p>
      {:else if filter === 'today'}
        <p>No tasks due today</p>
      {:else if filter === 'overdue'}
        <p>No overdue tasks! 🎉</p>
      {:else}
        <p>No tasks yet</p>
        <a href="/dashboard/tasks/new" class="btn-primary">Create your first task</a>
      {/if}
    </div>
  {:else}
    <div class="task-grid">
      {#each filteredTasks as task}
        <div class="task-card" class:completed={task.completed}>
          <div class="task-header">
            <input
              type="checkbox"
              class="task-check"
              checked={task.completed}
              on:change={() => toggleTask(task.id, task.completed)}
            />
            <div class="task-priority priority-{task.priority}">
              {getPriorityLabel(task.priority)}
            </div>
          </div>
          
          <div class="task-content">
            <h3 class="task-title" class:completed={task.completed}>{task.title}</h3>
            {#if task.subtitle}
              <p class="task-subtitle">{task.subtitle}</p>
            {/if}
            {#if task.notes}
              <p class="task-notes">{task.notes}</p>
            {/if}
          </div>
          
          <div class="task-meta">
            <div class="task-due-date">
              {formatDate(task.dueDate)}
            </div>
            {#if getCategoryName(task.categoryId)}
              <div class="task-category">
                {getCategoryName(task.categoryId)}
              </div>
            {/if}
          </div>
          
          <div class="task-actions">
            <button 
              class="action-btn edit"
              on:click={() => goto(`/dashboard/tasks/${task.id}/edit`)}
            >
              Edit
            </button>
            <button 
              class="action-btn delete"
              on:click={() => deleteTask(task.id)}
            >
              Delete
            </button>
          </div>
        </div>
      {/each}
    </div>
  {/if}
</div>

<style>
  .page-header {
    margin-bottom: 2rem;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
  }

  .page-subtitle {
    color: #718096;
    font-size: 1.125rem;
    margin: 0;
  }

  .btn-primary {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #4299e1, #3182ce);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
  }

  .btn-primary:hover {
    background: linear-gradient(135deg, #3182ce, #2c5aa0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
  }

  .filters-section {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .search-box {
    margin-bottom: 1rem;
  }

  .search-box input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
  }

  .search-box input:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
  }

  .filter-tabs {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .filter-tab {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .filter-tab:hover {
    border-color: #4299e1;
    background: #f7fafc;
  }

  .filter-tab.active {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
  }

  .tasks-container {
    min-height: 400px;
  }

  .task-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
  }

  .task-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    padding: 1.5rem;
    transition: all 0.2s;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .task-card.completed {
    opacity: 0.7;
  }

  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .task-check {
    width: 18px;
    height: 18px;
    border: 2px solid #cbd5e0;
    border-radius: 4px;
    cursor: pointer;
  }

  .task-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  .priority-0 {
    background: #ebf8ff;
    color: #3182ce;
  }

  .priority-1 {
    background: #e6fffa;
    color: #319795;
  }

  .priority-2 {
    background: #fed7d7;
    color: #c53030;
  }

  .task-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 0.5rem;
  }

  .task-title.completed {
    text-decoration: line-through;
    color: #718096;
  }

  .task-subtitle {
    color: #4a5568;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
  }

  .task-notes {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 1rem;
  }

  .task-meta {
    margin-bottom: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
  }

  .task-due-date {
    font-size: 0.875rem;
    color: #718096;
    margin-bottom: 0.25rem;
  }

  .task-category {
    font-size: 0.75rem;
    color: #4a5568;
    background: #f7fafc;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
  }

  .task-actions {
    display: flex;
    gap: 0.5rem;
  }

  .action-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
  }

  .action-btn.edit {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
  }

  .action-btn.edit:hover {
    background: #edf2f7;
  }

  .action-btn.delete {
    background: #fed7d7;
    color: #c53030;
  }

  .action-btn.delete:hover {
    background: #fbb6ce;
  }

  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #718096;
  }

  .empty-state p {
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
  }

  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      gap: 1rem;
    }

    .task-grid {
      grid-template-columns: 1fr;
    }

    .filter-tabs {
      justify-content: center;
    }

    .page-title {
      font-size: 1.875rem;
    }
  }
</style>
