import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { verifyJWT } from '$lib/server/auth.js';
import { createTask, getTasksByUserId } from '$lib/server/db/operations.js';

export const GET: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const url = new URL(request.url);
    const completed = url.searchParams.get('completed');
    const completedFilter = completed === 'true' ? true : completed === 'false' ? false : undefined;

    const tasks = await getTasksByUserId(payload.userId, completedFilter);

    return json({ tasks });
  } catch (error) {
    console.error('Get tasks error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};

export const POST: RequestHandler = async ({ request, cookies }) => {
  try {
    const token = cookies.get('auth-token');
    if (!token) {
      return json({ error: 'Unauthorized' }, { status: 401 });
    }

    const payload = verifyJWT(token);
    if (!payload) {
      return json({ error: 'Invalid token' }, { status: 401 });
    }

    const { title, subtitle, notes, priority, categoryId, dueDate, recurrenceRule } = await request.json();

    // Validate required fields
    if (!title || title.trim() === '') {
      return json({ error: 'Title is required' }, { status: 400 });
    }

    // Validate priority
    if (priority !== undefined && (priority < 0 || priority > 2)) {
      return json({ error: 'Priority must be 0 (low), 1 (normal), or 2 (high)' }, { status: 400 });
    }

    // Parse due date if provided
    let parsedDueDate = null;
    if (dueDate) {
      parsedDueDate = new Date(dueDate);
      if (isNaN(parsedDueDate.getTime())) {
        return json({ error: 'Invalid due date format' }, { status: 400 });
      }
    }

    const task = await createTask({
      userId: payload.userId,
      title: title.trim(),
      subtitle: subtitle?.trim() || null,
      notes: notes?.trim() || null,
      priority: priority || 0,
      categoryId: categoryId || null,
      dueDate: parsedDueDate,
      recurrenceRule: recurrenceRule || null
    });

    return json({ task }, { status: 201 });
  } catch (error) {
    console.error('Create task error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
};
