import { pgTable, text, timestamp, integer, boolean, uuid, jsonb, index } from 'drizzle-orm/pg-core';

export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  name: text('name'),
  timezone: text('timezone').default('Asia/Kuala_Lumpur'),
  emailFrequencyDays: integer('email_frequency_days').default(1),
  emailPreviewDays: integer('email_preview_days').default(1),
  dailyEmailCount: integer('daily_email_count').default(0),
  lastEmailDate: timestamp('last_email_date'),
  isVerified: boolean('is_verified').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => ({
  emailIdx: index('idx_users_email').on(table.email),
  timezoneIdx: index('idx_users_timezone').on(table.timezone),
}));

export const sessions = pgTable('sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  token: text('token').notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const otpRequests = pgTable('otp_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  requestId: uuid('request_id').notNull().unique(),
  email: text('email').notNull(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  code: text('code').notNull(),
  purpose: text('purpose').default('registration'), // 'registration' or 'password_reset'
  attempts: integer('attempts').default(0),
  isUsed: boolean('is_used').default(false),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const emailLimits = pgTable('email_limits', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull(),
  requestCount: integer('request_count').default(0),
  lastRequestDate: timestamp('last_request_date').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const officeFlowLinks = pgTable('office_flow_links', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  officeFlowUserId: text('office_flow_user_id').notNull(),
  officeFlowEmail: text('office_flow_email').notNull(),
  officeFlowName: text('office_flow_name'),
  officeFlowAvatar: text('office_flow_avatar'),
  officeFlowDepartment: text('office_flow_department'),
  officeFlowPosition: text('office_flow_position'),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  tokenExpiresAt: timestamp('token_expires_at'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Task categories
export const categories = pgTable('categories', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  name: text('name').notNull(),
  color: text('color').default('#6B7280'),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  userIdIdx: index('idx_categories_user_id').on(table.userId),
}));

// Tasks
export const tasks = pgTable('tasks', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  title: text('title').notNull(),
  subtitle: text('subtitle'),
  notes: text('notes'),
  priority: integer('priority').default(0), // 0: low, 1: normal, 2: high
  categoryId: uuid('category_id').references(() => categories.id, { onDelete: 'set null' }),
  dueDate: timestamp('due_date'),
  completed: boolean('completed').default(false),
  completedAt: timestamp('completed_at'),
  recurrenceRule: jsonb('recurrence_rule'), // JSON for complex recurrence rules
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => ({
  userIdIdx: index('idx_tasks_user_id').on(table.userId),
  dueDateIdx: index('idx_tasks_due_date').on(table.dueDate),
  completedIdx: index('idx_tasks_completed').on(table.completed),
  userDueIdx: index('idx_tasks_user_due').on(table.userId, table.dueDate),
  userCompletedIdx: index('idx_tasks_user_completed').on(table.userId, table.completed),
}));

// Email sending logs
export const emailLogs = pgTable('email_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  emailType: text('email_type').notNull(), // 'daily_summary', 'reminder'
  sentAt: timestamp('sent_at').defaultNow(),
  taskCount: integer('task_count').default(0),
  success: boolean('success').default(true),
}, (table) => ({
  userSentIdx: index('idx_email_logs_user_sent').on(table.userId, table.sentAt),
  sentAtIdx: index('idx_email_logs_sent_at').on(table.sentAt),
}));

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type OtpRequest = typeof otpRequests.$inferSelect;
export type NewOtpRequest = typeof otpRequests.$inferInsert;
export type EmailLimit = typeof emailLimits.$inferSelect;
export type NewEmailLimit = typeof emailLimits.$inferInsert;
export type OfficeFlowLink = typeof officeFlowLinks.$inferSelect;
export type NewOfficeFlowLink = typeof officeFlowLinks.$inferInsert;
export type Category = typeof categories.$inferSelect;
export type NewCategory = typeof categories.$inferInsert;
export type Task = typeof tasks.$inferSelect;
export type NewTask = typeof tasks.$inferInsert;
export type EmailLog = typeof emailLogs.$inferSelect;
export type NewEmailLog = typeof emailLogs.$inferInsert;
