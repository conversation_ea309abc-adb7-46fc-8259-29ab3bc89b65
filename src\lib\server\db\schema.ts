import { pgTable, text, timestamp, integer, boolean, uuid } from 'drizzle-orm/pg-core';

export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull().unique(),
  password: text('password').notNull(),
  name: text('name'),
  isVerified: boolean('is_verified').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const sessions = pgTable('sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  token: text('token').notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const otpRequests = pgTable('otp_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  requestId: uuid('request_id').notNull().unique(),
  email: text('email').notNull(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),
  code: text('code').notNull(),
  purpose: text('purpose').default('registration'), // 'registration' or 'password_reset'
  attempts: integer('attempts').default(0),
  isUsed: boolean('is_used').default(false),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const emailLimits = pgTable('email_limits', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: text('email').notNull(),
  requestCount: integer('request_count').default(0),
  lastRequestDate: timestamp('last_request_date').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const officeFlowLinks = pgTable('office_flow_links', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  officeFlowUserId: text('office_flow_user_id').notNull(),
  officeFlowEmail: text('office_flow_email').notNull(),
  officeFlowName: text('office_flow_name'),
  officeFlowAvatar: text('office_flow_avatar'),
  officeFlowDepartment: text('office_flow_department'),
  officeFlowPosition: text('office_flow_position'),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  tokenExpiresAt: timestamp('token_expires_at'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type OtpRequest = typeof otpRequests.$inferSelect;
export type NewOtpRequest = typeof otpRequests.$inferInsert;
export type EmailLimit = typeof emailLimits.$inferSelect;
export type NewEmailLimit = typeof emailLimits.$inferInsert;
export type OfficeFlowLink = typeof officeFlowLinks.$inferSelect;
export type NewOfficeFlowLink = typeof officeFlowLinks.$inferInsert;
